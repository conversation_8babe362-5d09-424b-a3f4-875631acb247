<template>
  <view class="jin-ju-card-page" :style="mix_diyStyle">
    <view class="container">
      <!-- 金句卡动画容器 -->
      <view class="card-container">
        <!-- 使用新的动画控制器 -->
        <CardAnimationController
          :cardImage="currentCardImage"
          :isAnimating="isAnimating"
          :animationType="currentAnimationType"
          :enableSound="true"
          @animationComplete="onAnimationComplete"
          ref="cardAnimation"
        />

        <!-- 传统图片显示（作为后备） -->
        <image
          v-if="!useAdvancedAnimation"
          class="card-image"
          :src="currentCardImage"
          mode="aspectFit"
          :class="{ 'card-flip': isFlipping }"
        />
      </view>
      
      <!-- 分享按钮 -->
      <view class="share-button-container">
        <view 
          class="share-button" 
          @click="generatePoster"
          :class="{ 'disabled': isGeneratingPoster }"
        >
          {{ isGeneratingPoster ? '生成中...' : '分享海报' }}
        </view>
      </view>
      
      <!-- 抽牌按钮 -->
      <view class="draw-button-container">
        <view 
          class="draw-button" 
          @click="drawCard"
          :class="{ 'disabled': isDrawing }"
        >
          {{ isDrawing ? '抽取中...' : '抽取金句卡' }}
        </view>
      </view>
      
      <!-- 活动banner -->
      <view class="banner-container">
        <image 
          class="banner-image" 
          src="https://sg-tduck-sh.oss-cn-shanghai.aliyuncs.com/jjk/imgs/jjk-jinqun.jpeg"
          mode="widthFix"
          @click="goToProduct"
        />
      </view>
    </view>
    
    <!-- 登录弹窗 -->
    <loginPop ref="loginPop"></loginPop>
    
    <!-- 海报分享弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-content" @click.stop>
        <view class="poster-header">
          <text class="poster-title">分享海报</text>
          <text class="close-btn" @click="closePosterModal">×</text>
        </view>
        <view class="poster-canvas-container">
          <canvas 
            canvas-id="posterCanvas" 
            class="poster-canvas"
            :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
          ></canvas>
        </view>
        <view class="poster-actions">
          <view class="action-btn save-btn" @click="savePoster">保存到相册</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import loginPop from '@/components/loginPop/loginPop.vue'
import CardAnimationController from '@/components/CardAnimation/CardAnimationController.vue'
import { drawCardResource, getResourceByRid, getUserInfo } from '@/api/camp.js'

export default {
  components: {
    loginPop,
    CardAnimationController
  },
  data() {
    return {
      currentCardImage: 'https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/0.webp',
      isDrawing: false,
      isFlipping: false,
      defaultCardImage: 'https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/0.webp',
      currentRid: null,
      isGeneratingPoster: false,
      showPosterModal: false,
      canvasWidth: 375,
      canvasHeight: 667,
      posterImagePath: '',
      // 新增动画相关状态
      isAnimating: false,
      currentAnimationType: 'draw',
      useAdvancedAnimation: true,
      animationSequence: []
    }
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  onLoad(options) {
    setTimeout(() => {
      uni.setNavigationBarTitle({
        title: '金句卡抽取'
      })
    }, 0)
    
    // 检查是否有rid参数
    if (options.rid) {
      this.currentRid = options.rid
      this.loadCardByRid(options.rid)
    }
  },
  onShow() {
    // #ifdef H5
    // H5端检查URL参数中的rid
    if (this.$Route && this.$Route.query && this.$Route.query.rid) {
      const rid = this.$Route.query.rid
      if (rid !== this.currentRid) {
        this.currentRid = rid
        this.loadCardByRid(rid)
      }
    }
    // #endif
    
    this.checkLogin()
  },
  methods: {
    // 检查登录状态
    checkLogin() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
      }
    },
    
    // 根据rid加载对应卡片
    async loadCardByRid(rid) {
      try {
        const response = await getResourceByRid(rid)
        if (response.state === 200 && response.data) {
          this.currentCardImage = response.data.imageUrl || `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${rid}.webp`
          this.currentRid = rid
        } else {
          // 后备方案
          if (rid >= 100 && rid <= 600) {
            this.currentCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${rid}.webp`
            this.currentRid = rid
          }
        }
      } catch (error) {
        console.error('加载卡片失败:', error)
        // 后备方案
        if (rid >= 100 && rid <= 600) {
          this.currentCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${rid}.webp`
          this.currentRid = rid
        }
      }
    },
    
    // 生成海报
    async generatePoster() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
        return
      }

      if (this.isGeneratingPoster) {
        return
      }

      this.isGeneratingPoster = true

      uni.showLoading({
        title: '生成海报中...'
      })

      try {
        // 获取用户信息
        const userResponse = await getUserInfo()
        const userInfo = userResponse.state === 200 ? userResponse.data : null

        // 生成海报
        setTimeout(() => {
          this.drawPosterCanvas(userInfo)
        }, 500)
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 不影响海报生成，继续执行
        setTimeout(() => {
          this.drawPosterCanvas(null)
        }, 500)
      }
    },
    
    // 绘制海报画布
    drawPosterCanvas(userInfo = null) {
      const ctx = uni.createCanvasContext('posterCanvas', this)
      
      // 设置背景色
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
      
      // 绘制标题
      ctx.fillStyle = '#333333'
      ctx.font = 'bold 24px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('金句卡分享', this.canvasWidth / 2, 50)
      
      // 绘制卡片图片
      if (this.currentCardImage && this.currentRid) {
        uni.getImageInfo({
          src: this.currentCardImage,
          success: (res) => {
            const imgWidth = 200
            const imgHeight = 280
            const imgX = (this.canvasWidth - imgWidth) / 2
            const imgY = 80
            
            ctx.drawImage(res.path, imgX, imgY, imgWidth, imgHeight)
            
            // 绘制卡片编号
            ctx.fillStyle = '#666666'
            ctx.font = '16px sans-serif'
            ctx.fillText(`卡片编号: ${this.currentRid}`, this.canvasWidth / 2, imgY + imgHeight + 30)

            // 绘制用户信息（如果有）
            if (userInfo && userInfo.nickname) {
              ctx.fillStyle = '#333333'
              ctx.font = 'bold 18px sans-serif'
              ctx.fillText(`${userInfo.nickname} 的金句卡`, this.canvasWidth / 2, imgY + imgHeight + 60)
            }

            // 绘制底部文字
            ctx.fillStyle = '#999999'
            ctx.font = '14px sans-serif'
            ctx.fillText('扫码体验更多精彩内容', this.canvasWidth / 2, this.canvasHeight - 50)
            
            ctx.draw(false, () => {
              this.isGeneratingPoster = false
              this.showPosterModal = true
              uni.hideLoading()
            })
          },
          fail: () => {
            this.isGeneratingPoster = false
            uni.hideLoading()
            this.$api.msg('生成海报失败')
          }
        })
      } else {
        this.isGeneratingPoster = false
        uni.hideLoading()
        this.$api.msg('请先抽取卡片')
      }
    },
    
    // 保存海报
    savePoster() {
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              this.$api.msg('海报已保存到相册')
              this.closePosterModal()
            },
            fail: () => {
              this.$api.msg('保存失败，请检查相册权限')
            }
          })
        },
        fail: () => {
          this.$api.msg('生成图片失败')
        }
      })
    },
    
    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false
    },
    
    // 抽卡函数 - 增强版本
    drawCard() {
      if (!this.hasLogin) {
        this.$refs.loginPop.openLogin()
        return
      }

      if (this.isDrawing) {
        return
      }

      this.isDrawing = true

      // 开始抽卡动画序列
      this.startDrawCardAnimation()
    },

    // 开始抽卡动画序列
    async startDrawCardAnimation() {
      try {
        // 阶段1: 准备动画
        this.currentAnimationType = 'draw'
        this.isAnimating = true

        // 调用抽卡API
        const response = await drawCardResource()

        let cardNumber, newCardImage

        if (response.state === 200 && response.data) {
          // 使用API返回的数据
          cardNumber = response.data.rid || response.data.id
          newCardImage = response.data.imageUrl || `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${cardNumber}.webp`
        } else {
          // 后备方案：生成随机卡片
          cardNumber = Math.floor(Math.random() * 500) + 100
          newCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${cardNumber}.webp`
        }

        // 更新rid到URL参数
        this.updateUrlWithRid(cardNumber)

        // 延迟更新卡片图片，让动画先开始
        setTimeout(() => {
          this.currentCardImage = newCardImage
          this.currentRid = cardNumber
        }, 800)

        // 阶段2: 揭示动画
        setTimeout(() => {
          this.currentAnimationType = 'reveal'
        }, 1500)

        // 阶段3: 庆祝动画
        setTimeout(() => {
          this.currentAnimationType = 'celebrate'
          this.$api.msg('恭喜获得金句卡！')
        }, 2500)

        // 结束动画
        setTimeout(() => {
          this.isAnimating = false
          this.isDrawing = false
          this.isFlipping = false
        }, 4000)

      } catch (error) {
        console.error('抽卡失败:', error)
        // 错误处理：使用后备方案
        const cardNumber = Math.floor(Math.random() * 500) + 100
        const newCardImage = `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${cardNumber}.webp`

        this.updateUrlWithRid(cardNumber)

        setTimeout(() => {
          this.currentCardImage = newCardImage
          this.currentRid = cardNumber
          this.currentAnimationType = 'reveal'
        }, 800)

        setTimeout(() => {
          this.currentAnimationType = 'celebrate'
          this.$api.msg('恭喜获得金句卡！')
        }, 1500)

        setTimeout(() => {
          this.isAnimating = false
          this.isDrawing = false
          this.isFlipping = false
        }, 3000)
      }
    },

    // 动画完成回调
    onAnimationComplete() {
      console.log('Animation completed:', this.currentAnimationType)
      // 可以在这里添加额外的逻辑
    },
    
    // 更新URL参数
    updateUrlWithRid(rid) {
      // #ifdef H5
      const currentUrl = new URL(window.location.href)
      currentUrl.searchParams.set('rid', rid)
      window.history.replaceState({}, '', currentUrl.toString())
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序端可以考虑使用其他方式保存状态，比如本地存储
      uni.setStorageSync('current_card_rid', rid)
      // #endif
    },
    
    // 跳转商品链接
    goToProduct() {
      // #ifdef H5
      window.open('https://baidu.com', '_blank')
      // #endif
      // #ifndef H5
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent('https://baidu.com')}`
      })
      // #endif
    },

    // 手动触发特定动画
    triggerDrawAnimation() {
      if (this.$refs.cardAnimation) {
        this.$refs.cardAnimation.startDrawAnimation()
      }
    },

    triggerRevealAnimation() {
      if (this.$refs.cardAnimation) {
        this.$refs.cardAnimation.startRevealAnimation()
      }
    },

    triggerCelebrationAnimation() {
      if (this.$refs.cardAnimation) {
        this.$refs.cardAnimation.startCelebrationAnimation()
      }
    },

    // 停止所有动画
    stopAllAnimations() {
      if (this.$refs.cardAnimation) {
        this.$refs.cardAnimation.stopAnimation()
      }
      this.isAnimating = false
      this.isDrawing = false
    }
  }
}
</script>

<style lang="scss" scoped>
.jin-ju-card-page {
  min-height: 100vh;
  background-image: url('https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/0.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    z-index: 1;
  }
}

.container {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  position: relative;
  z-index: 2;
}

.card-container {
  margin-top: 80rpx;
  margin-bottom: 100rpx;
  position: relative;
  width: 600rpx;
  height: 880rpx;
  margin-left: auto;
  margin-right: auto;

  // 为动画系统提供合适的容器
  display: flex;
  align-items: center;
  justify-content: center;

  // 添加一些视觉增强
  &::before {
    content: '';
    position: absolute;
    top: -20rpx;
    left: -20rpx;
    right: -20rpx;
    bottom: -20rpx;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    border-radius: 40rpx;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  &:hover::before {
    opacity: 1;
  }
}

.card-image {
  width: 600rpx;
  height: 880rpx;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  transition: transform 0.6s ease-in-out;

  &.card-flip {
    transform: rotateY(180deg);
  }
}

.share-button-container {
  margin-bottom: 30rpx;
}

.share-button {
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.4);
  transition: all 0.3s ease;
  
  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 15rpx rgba(76, 175, 80, 0.4);
  }
  
  &.disabled {
    opacity: 0.6;
    transform: none;
  }
}

.draw-button-container {
  margin-bottom: 120rpx;
}

.draw-button {
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  // 添加闪光效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg) translateX(-100%);
    transition: transform 0.6s ease;
  }

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 15rpx rgba(255, 215, 0, 0.4);

    &::before {
      transform: rotate(45deg) translateX(100%);
    }
  }

  &.disabled {
    opacity: 0.6;
    transform: none;

    &::before {
      display: none;
    }
  }

  // 抽卡时的脉冲效果
  &:not(.disabled):hover {
    animation: buttonPulse 2s ease-in-out infinite;
  }
}

.banner-container {
  margin-top: auto;
  width: 100%;
  padding: 0 20rpx;
}

.banner-image {
  width: 100%;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
}

.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.poster-content {
  width: 90%;
  max-width: 400rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.poster-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.poster-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.poster-canvas-container {
  padding: 30rpx;
  display: flex;
  justify-content: center;
}

.poster-canvas {
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
}

.poster-actions {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.save-btn {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
}

// 动画效果
@keyframes buttonPulse {
  0% {
    box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.4);
  }
  50% {
    box-shadow: 0 8rpx 25rpx rgba(255, 215, 0, 0.6), 0 0 30rpx rgba(255, 215, 0, 0.3);
  }
  100% {
    box-shadow: 0 6rpx 20rpx rgba(255, 215, 0, 0.4);
  }
}
</style>
