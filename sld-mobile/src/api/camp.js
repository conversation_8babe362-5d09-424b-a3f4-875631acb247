import request from '@/utils/request'

/**
 * 活动资源接口
 * 底层调用莘古表单系统
 * groupKey对应填表系统的formKey 比如NzfYPDDe
 * rid对应填表系统的 formKey+id. 比如NzfYPDDe01
 */

// 获取活动资源
export function getCampResource(params) {
  return request({
    url: 'v3/front/camp/resource',
    method: 'GET',
    data: params
  })
}

// 抽取卡片资源
export function drawCardResource(params) {
  return request({
    url: 'v3/front/camp/resource',
    method: 'POST',
    data: {
      groupKey: 'NzfYPDDe', // 彩虹卡资源组
      ...params
    }
  })
}

// 获取指定rid的资源
export function getResourceByRid(rid) {
  return request({
    url: 'v3/front/camp/resource',
    method: 'GET',
    data: {
      groupKey: 'NzfYPDDe',
      rid: rid
    }
  })
}

// 生成分享海报
export function generateSharePoster(params) {
  return request({
    url: 'v3/front/camp/poster',
    method: 'POST',
    data: params
  })
}

// 获取用户信息（用于海报生成）
export function getUserInfo() {
  return request({
    url: 'v3/member/front/info',
    method: 'GET'
  })
}
