{
	"pages": [
		// #ifdef H5
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "商城首页",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"navigationBarTextStyle": "white",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/camp/jjk/index",
			"style": {
				"navigationBarTitleText": "金句卡",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"navigationBarTextStyle": "white",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/index/information",
			"style": {
				"navigationBarTitleText": "商城首页",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"navigationBarTextStyle": "white",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		//#endif
		//app-1-start
		//app-1-end
		// #ifndef H5
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "商品",
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": true,
				"backgroundColor": "#ffffff",
				"app-plus": {
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/index/information",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"navigationBarTextStyle": "black",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
	
		//#endif
		{
			"path": "pages/index/topic",
			"style": {
				"navigationBarTitleText": "", //专题
				// #ifdef MP || H5
				"navigationStyle": "custom",
				//#endif
				"navigationBarTextStyle": "black"
			}
		},
		// #ifdef H5
		{
			"path": "pages/privacyPolicy/privacyPolicy",
			"style": {
				"navigationBarTitleText": "", //隐私协议
				"navigationStyle": "custom"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/privacyPolicy/privacyPolicy",
			"style": {
				"navigationBarTitleText": "" //隐私协议
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/set/set",
			"style": {
				"navigationBarTitleText": "", //设置
				"navigationStyle": "custom"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/set/set",
			"style": {
				"navigationBarTitleText": "" //设置
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/set/aboutUs",
			"style": {
				"navigationBarTitleText": "" //关于我们
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/set/aboutUs",
			"style": {
				"navigationBarTitleText": "", //关于我们
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//app-2-start
		//app-2-end
		// #ifdef H5
		{
			"path": "pages/index/skip_to",
			"style": {
				"navigationStyle": "custom"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/index/skip_to",
			"style": {}
		},
		//#endif
		// #ifndef MP
		{
			"path": "pages/cart/cart",
			"style": {
				"navigationBarTitleText": "购物车",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		// #endif
		//wx-1-start
		// #ifdef MP
		{
			"path": "pages/cart/cart",
			"style": {
				"navigationBarTitleText": "", //购物车
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		// #endif
		//wx-1-end
		// #ifdef H5
		{
			"path": "pages/public/brand",
			"style": {
				"navigationBarTitleText": "", //品牌
				"navigationStyle": "custom"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/public/brand",
			"style": {
				"navigationBarTitleText": "" //品牌
			}
		},
		// #endif
		//wx-2-start
		// #ifdef MP
		{
			"path": "pages/public/login",
			"style": {
				"navigationBarTitleText": "" //登录
			}
		},
		{
			"path": "pages/public/loginBlank",
			"style": {
				"navigationBarTitleText": "" //登录
			}
		},
		// #endif
		//wx-2-end
		// #ifndef MP
		{
			"path": "pages/public/login",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom",
					"softinputmode": "adjustPan"
				}
			}
		},
		{
			"path": "pages/public/loginBlank",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom",
					"softinputmode": "adjustPan"
				}
			}
		},
		// #endif
		//wx-3-start
		// #ifdef MP
		{
			"path": "pages/public/loginMobile",
			"style": {
				"navigationBarTitleText": "" //登录
			}
		},
		{
			"path": "pages/public/loginPassword",
			"style": {
				"navigationBarTitleText": "" //登录
			}
		},
		// #endif
		//wx-3-end
		// #ifndef MP
		{
			"path": "pages/public/loginPassword",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}
		},
		{
			"path": "pages/public/loginMobile",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}
		},
		// #endif
		{
			"path": "pages/public/bindMobile",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}
		},
		//#ifndef MP
		{
			"path": "pages/public/register",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}
		},
		//#endif
		//#ifdef MP
		{
			"path": "pages/public/register",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		//#endif
		//wx-4-end
		//#ifdef H5||APP-PLUS
		{
			"path": "pages/public/forgetPwd",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom",
					"softinputmode": "adjustPan"
				}
			}
		},
		//#endif
		//wx-5-start
		//#ifdef MP
		{
			"path": "pages/public/forgetPwd",
			"style": {
				"navigationBarTitleText": "" //找回密码
			}
		},
		//#endif
		//wx-5-end
		{
			"path": "pages/user/user",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"app-plus": {
					"titleNView": false,
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		//#ifdef H5
		{
			"path": "pages/user/changeInfo",
			"style": {
				"navigationBarTitleText": " ", //修改昵称
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/user/changeInfo",
			"style": {
				"navigationBarTitleText": " " //修改昵称
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/member/collect",
			"style": {
				"navigationBarTitleText": "", //我的收藏
				"onReachBottomDistance": 150,
				"app-plus": {
					"bounce": "none"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifdef H5
		{
			"path": "pages/member/collect",
			"style": {
				"navigationBarTitleText": "", //我的收藏
				"onReachBottomDistance": 150,
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/member/history",
			"style": {
				"navigationBarTitleText": "", //我的足迹
				"navigationStyle": "custom",
				"onReachBottomDistance": 150
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		//app-3-start
		//app-3-end
		//wx-6-start
		// #ifdef MP
		{
			"path": "pages/member/history",
			"style": {
				"navigationBarTitleText": "" //我的足迹
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		//wx-6-end
		// #ifdef H5
		{
			"path": "pages/user/info",
			"style": {
				"navigationBarTitleText": "", //个人信息
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/user/info",
			"style": {
				"navigationBarTitleText": "" //个人信息
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/account/account",
			"style": {
				"navigationBarTitleText": "账号安全" //账号安全
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/account/account",
			"style": {
				"navigationBarTitleText": "账号安全", //账号安全
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/account/changeMobile",
			"style": {
				"navigationBarTitleText": "" //身份验证
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/account/changeMobile",
			"style": {
				"navigationBarTitleText": "", //身份验证
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/account/bindEmail",
			"style": {
				"navigationBarTitleText": "" //绑定电子邮箱
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/account/bindEmail",
			"style": {
				"navigationBarTitleText": "", //绑定电子邮箱
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/account/changeEmail",
			"style": {
				"navigationBarTitleText": "", //身份验证
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/account/changeEmail",
			"style": {
				"navigationBarTitleText": "" //身份验证
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/account/managePwd",
			"style": {
				"navigationBarTitleText": "", //设置登录密码
				"app-plus": {
					"softinputmode": "adjustPan"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/account/managePwd",
			"style": {
				"navigationBarTitleText": "", //设置登录密码
				"navigationStyle": "custom",
				"app-plus": {
					"softinputmode": "adjustPan"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/account/changePwd",
			"style": {
				"navigationBarTitleText": "", //修改密码
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/account/changePwd",
			"style": {
				"navigationBarTitleText": "" //修改密码
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		{
			"path": "pages/order/list",
			"style": {
				// #ifdef H5
				"navigationStyle": "custom",
				// #endif
				"navigationBarTitleText": "", //我的订单
				"app-plus": {
					"bounce": "none"
				}
			},
			"meta": {
				//应对小程序新的审核机制(********),审核时需要设置订单path,因此不能去校验跳转
				// #ifndef MP-WEIXIN
				"checkLogin": "true"
				// #endif
			}
		},
		//wx-7-start
		// #ifdef MP
		{
			"path": "pages/order/detail",
			"style": {
				"navigationBarTitleText": "", //订单详情
				"navigationBarBackgroundColor": "#F2F2F2",
				"app-plus": {
					"titleNView": {
						"type": "transparent", //沉浸式导航
						"backButton": {
							"background": ""
						},
						"autoBackButton": true
					}
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		//wx-7-end
		// #ifdef H5
		{
			"path": "pages/order/detail",
			"style": {
				"navigationBarTitleText": "订单详情",
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		//app-4-start
		//app-4-end
		{
			"path": "pages/order/tradeSuccess",
			"style": {
				"navigationBarTitleText": "", //交易成功
				"app-plus": {
					"titleNView": {
						"type": "transparent" //沉浸式导航
					}
				}
			}
		},
		// #ifdef H5
		{
			"path": "pages/order/publishEvaluation",
			"style": {
				"navigationBarTitleText": "", //发表评价
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/order/publishEvaluation",
			"style": {
				"navigationBarTitleText": "", //发表评价
				"app-plus": {
					"bounce": "none"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/order/confirmOrder",
			"style": {
				"navigationBarTitleText": "", //确认订单
				"app-plus": {
					"softinputMode": "adjustPan"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/order/confirmOrder",
			"style": {
				"navigationBarTitleText": "", //确认订单
				"navigationStyle": "custom",
				"app-plus": {
					"softinputMode": "adjustPan"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/address/list",
			"style": {
				"navigationBarTitleText": "", //我的地址
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/address/list",
			"style": {
				"navigationBarTitleText": "" //我的地址
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/address/operate",
			"style": {
				"navigationBarTitleText": "" //新增收货地址
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/address/operate",
			"style": {
				"navigationBarTitleText": "", //新增收货地址
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/notice/noticeCenter",
			"style": {
				"navigationBarTitleText": "", //消息中心
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/notice/noticeCenter",
			"style": {
				"navigationBarTitleText": "" //消息中心
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/notice/notice",
			"style": {
				"navigationBarTitleText": "", //消息通知
				"enablePullDownRefresh": true
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/notice/notice",
			"style": {
				"navigationBarTitleText": "", //消息通知
				"navigationStyle": "custom",
				"enablePullDownRefresh": true
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/notice/receivingSet",
			"style": {
				"navigationBarTitleText": "", //接收设置
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/notice/receivingSet",
			"style": {
				"navigationBarTitleText": "" //接收设置
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		// #endif
		// #ifndef H5
		{
			"path": "pages/category/category",
			"style": {
				"navigationBarTitleText": "", //分类
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		// #endif
		// #ifdef H5
		{
			"path": "pages/category/category",
			"style": {
				"navigationBarTitleText": "", //分类
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		// #endif
		//wx-8-start
		// #ifdef MP
		{
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": "" //搜索
				// "navigationStyle": "custom",
				// "app-plus": {
				// 	"titleNView": false,
				// 	"animationType": "slide-in-bottom"
				// }
			}
		},
		//#endif
		//wx-8-end
		// #ifndef MP
		{
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/recharge/recharge",
			"style": {
				"navigationBarTitleText": "", //账户充值
				//手机软键盘升起不让其将页面头部上推
				"app-plus": {
					"softinputMode": "adjustResize"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/recharge/recharge",
			"style": {
				"navigationBarTitleText": "", //账户充值
				//手机软键盘升起不让其将页面头部上推
				"navigationStyle": "custom",
				"app-plus": {
					"softinputMode": "adjustResize"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/order/pay",
			"style": {
				"navigationBarTitleText": "", //收银台
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/order/pay",
			"style": {
				"navigationBarTitleText": "" //收银台
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/balance/balance",
			"style": {
				"navigationBarTitleText": "" //我的钱包
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/balance/balance",
			"style": {
				"navigationBarTitleText": "", //我的钱包
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/balance/list",
			"style": {
				"navigationBarTitleText": "", //余额明细
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/balance/list",
			"style": {
				"navigationBarTitleText": "" //余额明细
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/recharge/list",
			"style": {
				"navigationBarTitleText": "" //充值明细
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/recharge/list",
			"style": {
				"navigationBarTitleText": "", //充值明细
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifndef H5
		{
			"path": "pages/recharge/detail",
			"style": {
				"navigationBarTitleText": "" //充值详情
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		// #ifdef H5
		{
			"path": "pages/recharge/detail",
			"style": {
				"navigationBarTitleText": "", //充值详情
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		{
			"path": "pages/recharge/success",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/recharge/fail",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		//#ifdef H5
		{
			"path": "pages/invoice/myInvoice",
			"style": {
				"navigationBarTitleText": "", //我的发票
				"navigationStyle": "custom",
				"app-plus": {
					"softinputMode": "adjustPan"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/invoice/myInvoice",
			"style": {
				"navigationBarTitleText": "", //我的发票
				"app-plus": {
					"softinputMode": "adjustPan"
				}
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//app-5-start
		//app-5-end
		//#ifdef H5
		{
			"path": "pages/user/myIntegral",
			"style": {
				"navigationBarTitleText": "我的积分",
				"navigationStyle": "custom"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//wx-9-start
		//#ifdef MP
		{
			"path": "pages/user/myIntegral",
			"style": {
				"navigationBarTitleText": "我的积分"
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//wx-9-end
		//#ifdef H5
		{
			"path": "pages/public/codeLogin",
			"style": {
				"navigationBarTitleText": "扫码登录",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false,
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/public/codeLogin",
			"style": {
				"navigationBarTitleText": "扫码登录",
				"enablePullDownRefresh": false,
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		//#endif
		//app-6-start
		//app-6-end
		//#ifdef H5
		{
			"path": "pages/balance/outputList",
			"style": {
				"navigationBarTitleText": "", //提现记录
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/balance/outputList",
			"style": {
				"navigationBarTitleText": "", //提现记录
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifdef H5
		{
			"path": "pages/balance/outputInfo",
			"style": {
				"navigationBarTitleText": "", //提现记录详情
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/balance/outputInfo",
			"style": {
				"navigationBarTitleText": "", //提现记录详情
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifdef H5
		{
			"path": "pages/balance/account",
			"style": {
				"navigationBarTitleText": "", //提现账号管理
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef H5
		{
			"path": "pages/balance/account",
			"style": {
				"navigationBarTitleText": "", //提现账号管理
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		},
		//#endif
		//#ifndef MP
		{
			"path": "pages/graphic/graphicRelease",
			"style": {
				"navigationBarTitleText": "发布图文",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}
		},
		//#endif
		//#ifdef MP
		{
			"path": "pages/graphic/graphicRelease",
			"style": {
				"navigationBarTitleText": "发布图文"
			}
		},
		//#endif
		{
			"path": "pages/balance/add",
			"style": {
				// #ifdef H5 
				"navigationStyle": "custom",
				// #endif 
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		}, {
			"path": "pages/balance/outputDo",
			"style": {
				"navigationBarTitleText": "", //提现
				//#ifdef H5
				"navigationStyle": "custom",
				//#endif
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": "true"
			}
		}

	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#f8f8f8",
		"backgroundColorBottom": "#fff",
		"backgroundColorTop": "#fff",
		"rpxCalcMaxDeviceWidth": 640, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		"rpxCalcBaseDeviceWidth": 375, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
		"rpxCalcIncludeWidth": 749, // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
		"pageOrientation": "portrait",
		"scrollIndicator": "none",
		"app-plus": {
			"scrollIndicator": "none" //全局 在APP页面都不显示滚动条
		}
	},
	"tabBar": {
		"color": "#333",
		"selectedColor": "#89ba7f",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		// "spacing": "0px",
		// "midButton": {
		// 	"width": "56px",
		// 	"height": "56px",
		// 	"iconPath": "static/tab-bar/gouwuche.png",
		// 	"iconWidth": "56px"

		// },
		"list": [
			
			// #ifdef H5
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/tab-bar/home.png",
				"selectedIconPath": "static/tab-bar/home_active.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/category/category",
				"iconPath": "static/tab-bar/fenlei.png",
				"selectedIconPath": "static/tab-bar/fenlei_active.png",
				"text": "热门"
			},
			// #endif 
			// #ifndef H5 
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/tab-bar/home.png",
				"selectedIconPath": "static/tab-bar/home_active.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/index/information",
				"iconPath": "static/tab-bar/fenlei.png",
				"selectedIconPath": "static/tab-bar/fenlei_active.png",
				"text": "热门"
			},
			// #endif 
			
			{
				"pagePath": "pages/graphic/graphicRelease",
				"iconPath": "static/tab-bar/publish.png",
				"selectedIconPath": "static/tab-bar/publish.png",
				"iconWidth": "46px",
				"text": "发布"
			},
			{
				"pagePath": "pages/cart/cart",
				"iconPath": "static/tab-bar/gouwuche.png",
				"selectedIconPath": "static/tab-bar/gouwuche_active.png",
				"text": "购物车"
			},
			{
				"pagePath": "pages/user/user",
				"iconPath": "static/tab-bar/wode.png",
				"selectedIconPath": "static/tab-bar/wode_active.png",
				"text": "我的"
			}
		]
	},
	"subPackages": [{
			"root": "extra",
			"pages": [{
					"path": "user/my",
					"style": {
						"navigationBarTitleText": "", //我的视频
						// #ifdef MP || H5
						"navigationStyle": "custom",
						//#endif
						"app-plus": {
							"bounce": "none"
						},
						"h5": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "svideo/svideoSearch",
					"style": {
						"navigationBarTitleText": "视频搜索",
						"navigationStyle": "custom"
					}
				},
				// #ifdef H5
				{
					"path": "user/authorInfo",
					"style": {
						"navigationBarTitleText": "", //编辑资料
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				// #ifndef H5
				{
					"path": "user/authorInfo",
					"style": {
						"navigationBarTitleText": "" //编辑资料
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "user/attention",
					"style": {
						"navigationBarTitleText": "", //关注/粉丝
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "user/attention",
					"style": {
						"navigationBarTitleText": "" //关注/粉丝
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				{
					"path": "user/editAuthorInfo",
					"style": {
						// #ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //编辑资料
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//svideo-start
				{
					"path": "svideo/svideoList",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom", //短视频列表
						"navigationBarTextStyle": "white"
					}
				},
				// #ifdef H5
				{
					"path": "svideo/svideoSearch",
					"style": {
						"navigationBarTitleText": "", //短视频搜索
						"navigationStyle": "custom"
					}
				},
				//#endif
				// #ifndef H5
				// {
				// 	"path": "svideo/svideoSearch",
				// 	"style": {
				// 		"navigationBarTitleText": "" //短视频搜索
				// 	}
				// },
				//#endif
				// #ifndef H5
				{
					"path": "svideo/svideoAuthorList",
					"style": {
						"navigationBarTitleText": "" //更多用户
					}
				},
				//#endif
				// #ifdef H5
				{
					"path": "svideo/svideoAuthorList",
					"style": {
						"navigationBarTitleText": "", //更多用户
						"navigationStyle": "custom"
					}
				},
				//#endif
				// #ifdef H5
				{
					"path": "svideo/svideoRelease",
					"style": {
						"navigationBarTitleText": "", //发布短视频
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				// #ifndef H5
				{
					"path": "svideo/svideoRelease",
					"style": {
						"navigationBarTitleText": "" //发布短视频
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef MP
				{
					"path": "svideo/svideoSeleGoods",
					"style": {
						"navigationBarTitleText": "", //商品选择
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false,
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//wx-10-start
				//#ifdef MP
				{
					"path": "svideo/svideoSeleGoods",
					"style": {
						"navigationBarTitleText": "", //商品选择
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//wx-10-end
				{
					"path": "svideo/svideoPlay",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				//app-7-start
				//app-7-end
				//#ifdef H5
				{
					"path": "svideo/svideoComments",
					"style": {
						"navigationBarTitleText": "", //评论列表
						"navigationStyle": "custom"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "svideo/svideoComments",
					"style": {
						"navigationBarTitleText": "" //评论列表
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "svideo/svideoRecTopic",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "svideo/svideoRecTopic",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				//#endif
				//svideo-end
				// #ifdef MP
				{
					"path": "tshou/index/index",
					"style": {
						"navigationBarTitleText": "推手首页",
						"navigationBarTextStyle": "white",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//wx-11-end
				// #ifdef APP-PLUS ||H5
				{
					"path": "tshou/index/index",
					"style": {
						"navigationBarTitleText": "推手首页",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false,
							"bounce": "none"
						}
					}
				},
				//#endif
				// #ifdef H5
				{
					"path": "tshou/tsGift/giftPay",
					"style": {
						"navigationBarTitleText": "", //收银台
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				// #ifndef H5
				{
					"path": "tshou/tsGift/giftPay",
					"style": {
						"navigationBarTitleText": "" //收银台
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				// #ifdef APP-PLUS || H5
				{
					"path": "tshou/search/search",
					"style": {
						"navigationBarTitleText": "商品搜索",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//wx-12-start
				// #ifdef MP
				{
					"path": "tshou/search/search",
					"style": {
						"navigationBarTitleText": "商品搜索"
					}
				},
				// #endif
				//wx-12-end
				// #ifdef H5
				{
					"path": "tshou/goods/list",
					"style": {
						"navigationBarTitleText": "商品列表",
						"navigationStyle": "custom"
					}
				},
				// #endif
				//wx-13-start
				// #ifdef MP
				{
					"path": "tshou/goods/list",
					"style": {
						"navigationBarTitleText": "商品列表"
					}
				},
				// #endif
				//wx-13-end
				//app-10-start
				// #ifdef APP-PLUS 
				{
					"path": "tshou/goods/list",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//app-10-end
				// #ifdef H5 
				{
					"path": "tshou/goods/detail",
					"style": {
						"navigationBarTitleText": "", //商品详情
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "tshou/goods/detail",
					"style": {
						"navigationBarTitleText": "" //商品详情
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "tshou/tsGift/tsGift",
					"style": {
						"navigationBarTitleText": "", //推手礼包
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "tshou/tsGift/tsGift",
					"style": {
						"navigationBarTitleText": "" //推手礼包
					}
				},
				// #endif
				// #ifdef APP-PLUS || H5
				{
					"path": "tshou/mShop/mShop",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "tshou/mShop/shareShop",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//wx-14-start
				// #ifdef MP
				{
					"path": "tshou/mShop/mShop",
					"style": {
						"navigationBarTitleText": "" //我的小店
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "tshou/mShop/shareShop",
					"style": {
						"navigationBarTitleText": "Ta的小店"
					}
				},
				// #endif
				//wx-14-end
				// #ifdef APP-PLUS || H5
				{
					"path": "tshou/user/promote",
					"style": {
						"navigationBarTitleText": "", //我要推广
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				//wx-15-start
				// #ifdef MP
				{
					"path": "tshou/user/promote",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				//wx-15-end
				// #ifndef H5
				{
					"path": "tshou/comProblem/comList",
					"style": {
						"navigationBarTitleText": "" //分类
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "tshou/comProblem/comList",
					"style": {
						"navigationBarTitleText": "", //分类
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "tshou/comProblem/comDetail",
					"style": {
						"navigationBarTitleText": "", //问题详情
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "tshou/comProblem/comDetail",
					"style": {
						"navigationBarTitleText": "" //问题详情
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "tshou/comProblem/comCate",
					"style": {
						"navigationBarTitleText": "" //常见问题
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "tshou/comProblem/comCate",
					"style": {
						"navigationBarTitleText": "", //常见问题
						"navigationStyle": "custom"
					}
				},
				// #endif
				{
					"path": "tshou/commission/commission",
					"style": {
						"navigationBarTitleText": "推手佣金",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "tshou/user/user",
					"style": {
						"navigationBarTitleText": "个人中心",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "tshou/user/vip",
					"style": {
						"navigationBarTitleText": "", //推手等级
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #ifdef H5
				{
					"path": "tshou/user/order",
					"style": {
						"navigationBarTitleText": "推手订单", //推手订单
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "tshou/user/order",
					"style": {
						"navigationBarTitleText": "", //推手订单
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "tshou/user/mySub",
					"style": {
						"navigationBarTitleText": "" //我的下级
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "tshou/user/mySub",
					"style": {
						"navigationBarTitleText": "", //我的下级
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				{
					"path": "graphic/graphicDetail",
					"style": {
						// #ifdef H5
						"navigationStyle": "custom",
						// #endif
						"navigationBarTitleText": "" //图文详情
					}
				},
				{
					"path": "graphic/posterShare",
					"style": {
					  // #ifdef H5
					  "navigationStyle": "custom",
					  // #endif
					  // #ifdef MP
					  "navigationBarTitleText": "分享海报",
					  // #endif
					  "navigationBarTextStyle": "black"
					}
				  }
			]
		},
		{
			"root": "standard",
			"pages": [{
					"path": "point/index/index",
					"style": {
						"navigationBarTitleText": "积分商城",
						//wx-16-start
						// #ifdef MP
						"navigationStyle": "custom",
						//#endif
						//wx-16-end
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				//app-11-start
				//app-11-end
				// #ifdef APP-PLUS || H5
				{
					"path": "point/product/detail",
					"style": {
						"navigationBarTitleText": "详情展示",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//wx-17-start
				// #ifdef MP
				{
					"path": "point/product/detail",
					"style": {
						"navigationBarTitleText": "详情展示"
					}
				},
				// #endif
				//wx-17-end
				//#ifdef H5
				{
					"path": "point/search/search",
					"style": {
						"navigationBarTitleText": "商品搜索",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "point/search/search",
					"style": {
						"navigationBarTitleText": "商品搜索"
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "point/order/list",
					"style": {
						"navigationBarTitleText": "我的积分订单",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "point/order/list",
					"style": {
						"navigationBarTitleText": "我的积分订单"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				{
					"path": "point/order/detail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"app-plus": {
							"titleNView": {
								"type": "transparent" //沉浸式导航
							}
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#ifndef H5
				{
					"path": "point/order/lookLogistics",
					"style": {
						"navigationBarTitleText": "查看物流"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "point/order/lookLogistics",
					"style": {
						"navigationBarTitleText": "查看物流",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef MP
				{
					"path": "point/search/good_list",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				//#endif
				//wx-18-start
				//#ifdef MP
				{
					"path": "point/search/good_list",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				//#endif
				//wx-18-end
				//#ifdef H5
				{
					"path": "point/product/confirm_order",
					"style": {
						"navigationBarTitleText": "确认订单",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "point/product/confirm_order",
					"style": {
						"navigationBarTitleText": "确认订单"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "point/product/pay",
					"style": {
						"navigationBarTitleText": "收银台"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "point/product/pay",
					"style": {
						"navigationBarTitleText": "收银台",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				{
					"path": "presale/index/list",
					"style": {
						"navigationBarTitleText": "",
						"navigationBarTextStyle": "white",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				{
					"path": "ladder/index/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				//#ifdef H5
				{
					"path": "ladder/agreement/agreement",
					"style": {
						"navigationBarTitleText": "阶梯团定金协议",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "ladder/agreement/agreement",
					"style": {
						"navigationBarTitleText": "阶梯团定金协议"
					}
				},
				//#endif
				{
					"path": "pinGroup/index/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				//#ifndef H5
				{
					"path": "presale/agreement/agreement",
					"style": {
						"navigationBarTitleText": "预售定金协议"
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "presale/agreement/agreement",
					"style": {
						"navigationBarTitleText": "预售定金协议",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//#ifdef H5||APP-PLUS
				{
					"path": "store/shopHomePage",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				//#endif
				//wx-19-start
				//#ifdef MP
				{
					"path": "store/shopHomePage",
					"style": {
						"navigationBarTitleText": "店铺首页",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//wx-19-end
				//#ifdef H5
				{
					"path": "store/attentionStore",
					"style": {
						"navigationBarTitleText": "关注店铺",
						"navigationStyle": "custom",
						"app-plus": {
							"scrollIndicator": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "store/attentionStore",
					"style": {
						"navigationBarTitleText": "关注店铺",
						"app-plus": {
							"scrollIndicator": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifdef H5||APP-PLUS
				{
					"path": "store/productSearch",
					"style": {
						"navigationBarTitleText": "商品搜索",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//wx-20-start
				// #ifdef MP
				{
					"path": "store/productSearch",
					"style": {
						"navigationBarTitleText": "商品搜索"
					}
				},
				// #endif
				//wx-20-end
				// #ifdef H5
				{
					"path": "store/storeIntroduction",
					"style": {
						"navigationBarTitleText": "店铺信息",
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "store/storeIntroduction",
					"style": {
						"navigationBarTitleText": "店铺信息"
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "store/list",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "店铺街"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "store/list",
					"style": {
						"navigationBarTitleText": "店铺街"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "chat/list",
					"style": {
						"navigationBarTitleText": "消息中心",
						"onReachBottomDistance": 50,
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				// #ifdef H5
				{
					"path": "chat/list",
					"style": {
						"navigationBarTitleText": "消息中心",
						"navigationStyle": "custom",
						"onReachBottomDistance": 50
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				//app-12-start
				//app-12-end
				// #ifdef H5
				{
					"path": "chat/detail",
					"style": {
						"navigationBarTitleText": "聊天详情",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				//wx-21-start
				// #ifdef MP
				{
					"path": "chat/detail",
					"style": {
						"navigationBarTitleText": "聊天详情"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				// #endif
				//wx-21-end
				// #ifdef APP-PLUS || H5
				{
					"path": "product/detail",
					"style": {
						"navigationBarTitleText": "详情展示",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//wx-22-start
				// #ifdef MP
				{
					"path": "product/detail",
					"style": {
						"navigationBarTitleText": "详情展示",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				// #endif
				//wx-22-end
				// #ifdef H5
				{
					"path": "product/video",
					"style": {
						"navigationBarTitleText": "视频播放",
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "product/video",
					"style": {
						"navigationBarTitleText": "视频播放"
					}
				},
				// #endif
				{
					"path": "product/list",
					"style": {
						// #ifdef H5 || APP-PLUS
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false,
							"scrollIndicator": "none"
						},
						// #endif
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "商品列表"
					}
				},
				// #ifdef H5
				{
					"path": "product/evaluation",
					"style": {
						"navigationBarTitleText": "商品评价",
						"navigationStyle": "custom"
					}
				},
				// #endif
				// #ifndef H5
				{
					"path": "product/evaluation",
					"style": {
						"navigationBarTitleText": "商品评价"
					}
				},
				// #endif
				//#ifndef MP
				{
					"path": "rank/detail",
					"style": {
						"navigationBarTitleText": "榜单详情",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				//#endif
				//wx-23-start
				//#ifdef MP
				{
					"path": "rank/detail",
					"style": {
						"navigationBarTitleText": "榜单详情",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//wx-23-end
				//#ifndef MP
				{
					"path": "rank/aggr",
					"style": {
						"navigationBarTitleText": "排行榜",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					}
				},
				//#endif
				//wx-24-start
				//#ifdef MP
				{
					"path": "rank/aggr",
					"style": {
						"navigationBarTitleText": "排行榜",
						"navigationStyle": "custom"
					}
				},
				//#endif
				//wx-24-end
				//#ifndef MP
				{
					"path": "lottery/detail",
					"style": {
						"navigationBarTitleText": "抽奖",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false,
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//wx-25-start
				//#ifdef MP
				{
					"path": "lottery/detail",
					"style": {
						"navigationBarTitleText": "抽奖",
						"navigationStyle": "custom"
						// "navigationBarTitleText": "抽奖"
					}
					// "meta":{
					// 	"checkLogin":"true"
					// }
				},
				//#endif
				//wx-25-end
				//#ifdef H5
				{
					"path": "lottery/lotRec",
					"style": {
						"navigationBarTitleText": "我的中奖记录",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "lottery/lotRec",
					"style": {
						"navigationBarTitleText": "我的中奖记录"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				{
					"path": "signIn/signIn",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#ifdef H5
				{
					"path": "coupon/myCoupon",
					"style": {
						"navigationBarTitleText": "优惠券",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "coupon/myCoupon",
					"style": {
						"navigationBarTitleText": "优惠券"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				//#endif
				//#ifdef H5
				{
					"path": "seckill/seckill",
					"style": {
						"navigationBarTitleText": "秒杀首页",
						"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				//#endif
				//#ifndef H5
				{
					"path": "seckill/seckill",
					"style": {
						"navigationBarTitleText": "秒杀首页",
						"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				//#endif
				//app-13-start
				//app-13-end
				{
					"path": "coupon/couponCenter",
					"style": {
						"navigationStyle": "custom",
						//#ifndef H5
						"navigationBarTextStyle": "white",
						//#endif
						"navigationBarTitleText": "领券中心"
					}
				},
				{
					"path": "refund/applyRefund",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //申请退款
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "refund/refundDetail",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "退款详情" //退款详情
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "memLev/index",
					"style": {
						"navigationBarTitleText": "会员等级",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "memLev/rule",
					"style": {
						"navigationBarTitleText": "等级规则"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "memLev/equity",
					"style": {
						"navigationBarTitleText": "全部权益"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "refund/selectService",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //选择服务
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "refund/batchSel",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //售后商品选择
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "refund/progressDetail",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "", //进度详情
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "refund/returnAndRefundList",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "", //退款/售后
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "super/index",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "super/history",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "我的会员"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "super/pay",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "super/superPay",
					"style": {
						"navigationBarTitleText": "收银台"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "super/policy",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "付费会员用户协议"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "super/tradeSuccess",
					"style": {
						"navigationBarTitleText": "支付成功",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "logistics/logisticsBill",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //填写物流单号
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "logistics/lookLogistics",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //查看物流
					},
					"meta": {
						"checkLogin": "true"
					}
				},
				{
					"path": "logistics/logisticsCompany",
					"style": {
						//#ifdef H5
						"navigationStyle": "custom",
						//#endif
						"navigationBarTitleText": "" //物流公司
					},
					"meta": {
						"checkLogin": "true"
					}
				}
			]
		}
	]
}