<template>
  <view class="threejs-card-animation" v-if="isH5">
    <canvas 
      ref="threeCanvas" 
      class="three-canvas"
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
    ></canvas>
    
    <!-- Particle overlay for extra effects -->
    <view class="particle-overlay">
      <view 
        v-for="(particle, index) in particles" 
        :key="index"
        class="particle"
        :class="'particle-' + particle.type"
        :style="particle.style"
      ></view>
    </view>
  </view>
</template>

<script>
// #ifdef H5
import * as THREE from 'three'
// #endif

export default {
  name: 'ThreeJSCardAnimation',
  props: {
    cardImage: {
      type: String,
      default: ''
    },
    isAnimating: {
      type: Boolean,
      default: false
    },
    animationType: {
      type: String,
      default: 'draw' // 'draw', 'reveal', 'celebrate'
    }
  },
  data() {
    return {
      isH5: false,
      canvasWidth: 375,
      canvasHeight: 600,
      scene: null,
      camera: null,
      renderer: null,
      cardMesh: null,
      particles: [],
      animationId: null,
      isInitialized: false
    }
  },
  mounted() {
    // #ifdef H5
    this.isH5 = true
    this.$nextTick(() => {
      this.initThreeJS()
    })
    // #endif
  },
  beforeUnmount() {
    this.cleanup()
  },
  watch: {
    isAnimating(newVal) {
      if (newVal && this.isH5) {
        this.startAnimation()
      } else {
        this.stopAnimation()
      }
    },
    cardImage(newVal) {
      if (newVal && this.isH5) {
        this.updateCardTexture(newVal)
      }
    }
  },
  methods: {
    // #ifdef H5
    initThreeJS() {
      try {
        // Check if canvas ref is available
        if (!this.$refs.threeCanvas) {
          console.error('Canvas ref not available')
          this.isH5 = false
          return
        }

        // Scene setup
        this.scene = new THREE.Scene()
        this.scene.background = new THREE.Color(0x000000)
        this.scene.background.setAlpha(0)

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
          75,
          this.canvasWidth / this.canvasHeight,
          0.1,
          1000
        )
        this.camera.position.z = 5

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({
          canvas: this.$refs.threeCanvas,
          alpha: true,
          antialias: true
        })
        this.renderer.setSize(this.canvasWidth, this.canvasHeight)
        this.renderer.setPixelRatio(window.devicePixelRatio || 1)
        
        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
        this.scene.add(ambientLight)
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
        directionalLight.position.set(10, 10, 5)
        this.scene.add(directionalLight)
        
        // Create card geometry
        this.createCard()
        
        // Create particle system
        this.createParticleSystem()
        
        this.isInitialized = true
        this.animate()
        
      } catch (error) {
        console.error('Three.js initialization failed:', error)
        this.isH5 = false
      }
    },
    
    createCard() {
      const geometry = new THREE.PlaneGeometry(2, 3)
      const material = new THREE.MeshLambertMaterial({ 
        color: 0xffffff,
        transparent: true,
        opacity: 0.9
      })
      
      this.cardMesh = new THREE.Mesh(geometry, material)
      this.cardMesh.position.set(0, 0, 0)
      this.scene.add(this.cardMesh)
      
      // Add card back
      const backGeometry = new THREE.PlaneGeometry(2, 3)
      const backMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x4a4a4a,
        transparent: true,
        opacity: 0.8
      })
      
      this.cardBackMesh = new THREE.Mesh(backGeometry, backMaterial)
      this.cardBackMesh.position.set(0, 0, -0.01)
      this.cardBackMesh.rotation.y = Math.PI
      this.scene.add(this.cardBackMesh)
    },
    
    createParticleSystem() {
      const particleCount = 100
      const geometry = new THREE.BufferGeometry()
      const positions = new Float32Array(particleCount * 3)
      const colors = new Float32Array(particleCount * 3)
      
      for (let i = 0; i < particleCount; i++) {
        positions[i * 3] = (Math.random() - 0.5) * 10
        positions[i * 3 + 1] = (Math.random() - 0.5) * 10
        positions[i * 3 + 2] = (Math.random() - 0.5) * 10
        
        colors[i * 3] = Math.random()
        colors[i * 3 + 1] = Math.random()
        colors[i * 3 + 2] = Math.random()
      }
      
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
      
      const material = new THREE.PointsMaterial({
        size: 0.05,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
      })
      
      this.particleSystem = new THREE.Points(geometry, material)
      this.scene.add(this.particleSystem)
    },
    
    updateCardTexture(imageUrl) {
      if (!this.cardMesh) return
      
      const loader = new THREE.TextureLoader()
      loader.load(imageUrl, (texture) => {
        this.cardMesh.material.map = texture
        this.cardMesh.material.needsUpdate = true
      })
    },
    
    startAnimation() {
      if (!this.isInitialized) return
      
      switch (this.animationType) {
        case 'draw':
          this.animateCardDraw()
          break
        case 'reveal':
          this.animateCardReveal()
          break
        case 'celebrate':
          this.animateCelebration()
          break
      }
      
      this.createCSSParticles()
    },
    
    animateCardDraw() {
      // Camera zoom in
      const startPos = { z: 5 }
      const endPos = { z: 3 }
      
      this.animateValue(startPos, endPos, 1000, (values) => {
        this.camera.position.z = values.z
      })
      
      // Card flip animation
      setTimeout(() => {
        this.animateCardFlip()
      }, 500)
    },
    
    animateCardFlip() {
      const startRot = { y: 0 }
      const endRot = { y: Math.PI }
      
      this.animateValue(startRot, endRot, 800, (values) => {
        this.cardMesh.rotation.y = values.y
      })
    },
    
    animateCardReveal() {
      // Particle burst
      this.animateParticleBurst()

      // Card glow effect
      this.animateCardGlow()
    },

    animateCelebration() {
      // Camera shake
      this.animateCameraShake()

      // Particle explosion
      this.animateParticleExplosion()
    },

    animateParticleBurst() {
      if (!this.particleSystem) return

      const positions = this.particleSystem.geometry.attributes.position.array
      const originalPositions = [...positions]

      // Animate particles bursting outward
      const startTime = Date.now()
      const duration = 1000

      const animateBurst = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        for (let i = 0; i < positions.length; i += 3) {
          const originalX = originalPositions[i]
          const originalY = originalPositions[i + 1]
          const originalZ = originalPositions[i + 2]

          // Calculate burst direction
          const distance = Math.sqrt(originalX * originalX + originalY * originalY + originalZ * originalZ)
          const normalizedX = originalX / distance
          const normalizedY = originalY / distance
          const normalizedZ = originalZ / distance

          // Apply burst effect
          const burstStrength = progress * 5
          positions[i] = originalX + normalizedX * burstStrength
          positions[i + 1] = originalY + normalizedY * burstStrength
          positions[i + 2] = originalZ + normalizedZ * burstStrength
        }

        this.particleSystem.geometry.attributes.position.needsUpdate = true

        if (progress < 1) {
          requestAnimationFrame(animateBurst)
        } else {
          // Reset positions
          for (let i = 0; i < positions.length; i++) {
            positions[i] = originalPositions[i]
          }
          this.particleSystem.geometry.attributes.position.needsUpdate = true
        }
      }

      animateBurst()
    },

    animateCardGlow() {
      if (!this.cardMesh || !this.cardMesh.material) return

      // Ensure material has emissive property
      if (!this.cardMesh.material.emissive) {
        this.cardMesh.material.emissive = new THREE.Color(0x000000)
      }

      const originalEmissive = this.cardMesh.material.emissive.clone()
      const glowColor = new THREE.Color(0xffd700)

      const startTime = Date.now()
      const duration = 2000

      const animateGlow = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // Pulse effect
        const intensity = Math.sin(progress * Math.PI * 4) * 0.3 + 0.3
        this.cardMesh.material.emissive.lerpColors(originalEmissive, glowColor, intensity)

        if (progress < 1) {
          requestAnimationFrame(animateGlow)
        } else {
          this.cardMesh.material.emissive.copy(originalEmissive)
        }
      }

      animateGlow()
    },

    animateCameraShake() {
      if (!this.camera) return

      const originalPosition = this.camera.position.clone()
      const startTime = Date.now()
      const duration = 500

      const animateShake = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        if (progress < 1) {
          const shakeIntensity = (1 - progress) * 0.1
          this.camera.position.x = originalPosition.x + (Math.random() - 0.5) * shakeIntensity
          this.camera.position.y = originalPosition.y + (Math.random() - 0.5) * shakeIntensity
          this.camera.position.z = originalPosition.z + (Math.random() - 0.5) * shakeIntensity

          requestAnimationFrame(animateShake)
        } else {
          this.camera.position.copy(originalPosition)
        }
      }

      animateShake()
    },

    animateParticleExplosion() {
      if (!this.particleSystem) return

      const material = this.particleSystem.material
      const originalSize = material.size
      const originalOpacity = material.opacity

      const startTime = Date.now()
      const duration = 1500

      const animateExplosion = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        // Expand and fade
        material.size = originalSize * (1 + progress * 3)
        material.opacity = originalOpacity * (1 - progress)

        if (progress < 1) {
          requestAnimationFrame(animateExplosion)
        } else {
          // Reset
          material.size = originalSize
          material.opacity = originalOpacity
        }
      }

      animateExplosion()
    },
    
    animateValue(start, end, duration, callback) {
      const startTime = Date.now()
      
      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        const eased = this.easeOutCubic(progress)
        const current = {}
        
        for (let key in start) {
          current[key] = start[key] + (end[key] - start[key]) * eased
        }
        
        callback(current)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      
      animate()
    },
    
    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3)
    },
    
    animate() {
      if (!this.renderer || !this.scene || !this.camera) return
      
      this.animationId = requestAnimationFrame(() => this.animate())
      
      // Rotate particles
      if (this.particleSystem) {
        this.particleSystem.rotation.y += 0.01
      }
      
      this.renderer.render(this.scene, this.camera)
    },
    
    createCSSParticles() {
      this.particles = []
      
      for (let i = 0; i < 20; i++) {
        const particle = {
          type: Math.random() > 0.5 ? 'sparkle' : 'star',
          style: {
            left: Math.random() * 100 + '%',
            top: Math.random() * 100 + '%',
            animationDelay: Math.random() * 2 + 's',
            animationDuration: (Math.random() * 2 + 1) + 's'
          }
        }
        this.particles.push(particle)
      }
      
      // Clear particles after animation
      setTimeout(() => {
        this.particles = []
      }, 3000)
    },
    
    stopAnimation() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
      }
      this.particles = []
    },
    
    cleanup() {
      this.stopAnimation()
      
      if (this.renderer) {
        this.renderer.dispose()
      }
      
      if (this.scene) {
        this.scene.clear()
      }
    }
    // #endif
  }
}
</script>

<style lang="scss" scoped>
.threejs-card-animation {
  position: relative;
  width: 100%;
  height: 100%;
}

.three-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.particle-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  pointer-events: none;
}

.particle-sparkle {
  background: radial-gradient(circle, #fff, #ffd700);
  box-shadow: 0 0 6px #ffd700;
  animation: sparkle 2s ease-out forwards;
}

.particle-star {
  background: radial-gradient(circle, #fff, #00d4ff);
  box-shadow: 0 0 8px #00d4ff;
  animation: starBurst 1.5s ease-out forwards;
}

@keyframes sparkle {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

@keyframes starBurst {
  0% {
    opacity: 0;
    transform: scale(0) translateY(0);
  }
  50% {
    opacity: 1;
    transform: scale(2) translateY(-20px);
  }
  100% {
    opacity: 0;
    transform: scale(0) translateY(-40px);
  }
}
</style>
