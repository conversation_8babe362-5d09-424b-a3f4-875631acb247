<template>
  <view class="css-card-animation">
    <!-- Main card container -->
    <view class="card-scene" :class="{ 'is-animating': isAnimating }">
      <view class="card-container" :class="animationClass">
        <view class="card-face card-front">
          <image 
            v-if="cardImage" 
            :src="cardImage" 
            class="card-image"
            mode="aspectFit"
          />
          <view v-else class="card-placeholder">
            <text>金句卡</text>
          </view>
        </view>
        <view class="card-face card-back">
          <view class="card-back-design">
            <view class="card-pattern"></view>
            <text class="card-back-text">金句卡</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- Particle effects -->
    <view class="particle-container" v-if="showParticles">
      <view 
        v-for="(particle, index) in particles" 
        :key="index"
        class="particle"
        :class="'particle-' + particle.type"
        :style="particle.style"
      ></view>
    </view>
    
    <!-- Magic circle effect -->
    <view class="magic-circle" v-if="showMagicCircle" :class="{ 'active': isAnimating }">
      <view class="circle-ring ring-1"></view>
      <view class="circle-ring ring-2"></view>
      <view class="circle-ring ring-3"></view>
    </view>
    
    <!-- Light rays -->
    <view class="light-rays" v-if="showLightRays" :class="{ 'active': isAnimating }">
      <view v-for="n in 8" :key="n" class="light-ray" :style="{ transform: `rotate(${n * 45}deg)` }"></view>
    </view>
    
    <!-- Energy burst -->
    <view class="energy-burst" v-if="showEnergyBurst" :class="{ 'active': isAnimating }">
      <view class="burst-ring"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CSSCardAnimation',
  props: {
    cardImage: {
      type: String,
      default: ''
    },
    isAnimating: {
      type: Boolean,
      default: false
    },
    animationType: {
      type: String,
      default: 'draw' // 'draw', 'reveal', 'celebrate'
    }
  },
  data() {
    return {
      particles: [],
      showParticles: false,
      showMagicCircle: false,
      showLightRays: false,
      showEnergyBurst: false,
      animationClass: ''
    }
  },
  watch: {
    isAnimating(newVal) {
      if (newVal) {
        this.startAnimation()
      } else {
        this.stopAnimation()
      }
    }
  },
  methods: {
    startAnimation() {
      this.resetEffects()
      
      switch (this.animationType) {
        case 'draw':
          this.animateCardDraw()
          break
        case 'reveal':
          this.animateCardReveal()
          break
        case 'celebrate':
          this.animateCelebration()
          break
      }
    },
    
    animateCardDraw() {
      // Phase 1: Magic circle appears
      this.showMagicCircle = true
      this.animationClass = 'pre-draw'
      
      setTimeout(() => {
        // Phase 2: Card flip with particles
        this.animationClass = 'drawing'
        this.createDrawParticles()
        this.showParticles = true
      }, 500)
      
      setTimeout(() => {
        // Phase 3: Card revealed
        this.animationClass = 'drawn'
        this.showLightRays = true
      }, 1300)
      
      setTimeout(() => {
        // Phase 4: Settle
        this.animationClass = 'settled'
      }, 2000)
    },
    
    animateCardReveal() {
      this.showEnergyBurst = true
      this.animationClass = 'revealing'
      this.createRevealParticles()
      this.showParticles = true
      
      setTimeout(() => {
        this.animationClass = 'revealed'
        this.showLightRays = true
      }, 800)
    },
    
    animateCelebration() {
      this.animationClass = 'celebrating'
      this.showEnergyBurst = true
      this.showLightRays = true
      this.createCelebrationParticles()
      this.showParticles = true
      
      setTimeout(() => {
        this.animationClass = 'celebration-peak'
      }, 500)
      
      setTimeout(() => {
        this.animationClass = 'celebration-end'
      }, 1500)
    },
    
    createDrawParticles() {
      this.particles = []
      
      for (let i = 0; i < 15; i++) {
        const particle = {
          type: 'magic',
          style: {
            left: (Math.random() * 80 + 10) + '%',
            top: (Math.random() * 80 + 10) + '%',
            animationDelay: (Math.random() * 0.5) + 's',
            animationDuration: (Math.random() * 1 + 1.5) + 's'
          }
        }
        this.particles.push(particle)
      }
    },
    
    createRevealParticles() {
      this.particles = []
      
      for (let i = 0; i < 20; i++) {
        const particle = {
          type: Math.random() > 0.5 ? 'sparkle' : 'star',
          style: {
            left: (Math.random() * 100) + '%',
            top: (Math.random() * 100) + '%',
            animationDelay: (Math.random() * 0.3) + 's',
            animationDuration: (Math.random() * 1 + 1) + 's'
          }
        }
        this.particles.push(particle)
      }
    },
    
    createCelebrationParticles() {
      this.particles = []
      
      for (let i = 0; i < 30; i++) {
        const types = ['confetti', 'sparkle', 'star', 'diamond']
        const particle = {
          type: types[Math.floor(Math.random() * types.length)],
          style: {
            left: (Math.random() * 100) + '%',
            top: (Math.random() * 100) + '%',
            animationDelay: (Math.random() * 1) + 's',
            animationDuration: (Math.random() * 2 + 1) + 's'
          }
        }
        this.particles.push(particle)
      }
    },
    
    stopAnimation() {
      this.resetEffects()
      this.animationClass = ''
    },
    
    resetEffects() {
      this.particles = []
      this.showParticles = false
      this.showMagicCircle = false
      this.showLightRays = false
      this.showEnergyBurst = false
    }
  }
}
</script>

<style lang="scss" scoped>
.css-card-animation {
  position: relative;
  width: 100%;
  height: 100%;
  perspective: 1000px;
  overflow: hidden;
}

.card-scene {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-container {
  position: relative;
  width: 300rpx;
  height: 440rpx;
  transform-style: preserve-3d;
  transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
  
  &.pre-draw {
    transform: scale(0.9) rotateY(0deg);
    animation: preDraw 0.5s ease-out;
  }
  
  &.drawing {
    transform: scale(1.1) rotateY(180deg);
    animation: cardFlip 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
  }
  
  &.drawn {
    transform: scale(1) rotateY(180deg);
    animation: cardSettle 0.7s ease-out;
  }
  
  &.settled {
    transform: scale(1) rotateY(180deg);
  }
  
  &.revealing {
    animation: cardReveal 0.8s ease-out;
  }
  
  &.revealed {
    transform: scale(1.05);
    animation: cardGlow 2s ease-in-out infinite alternate;
  }
  
  &.celebrating {
    animation: cardCelebrate 1.5s ease-in-out;
  }
  
  &.celebration-peak {
    transform: scale(1.2) rotateY(180deg);
    animation: celebrationPeak 0.5s ease-out;
  }
  
  &.celebration-end {
    transform: scale(1) rotateY(180deg);
    animation: celebrationEnd 1s ease-out;
  }
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.card-front {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: rotateY(180deg);
}

.card-back {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.card-back-design {
  text-align: center;
  color: white;
}

.card-pattern {
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  border-radius: 50%;
  margin: 0 auto 20rpx;
  animation: patternPulse 2s ease-in-out infinite;
}

.card-back-text {
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.particle {
  position: absolute;
  pointer-events: none;
}

.particle-magic {
  width: 6rpx;
  height: 6rpx;
  background: radial-gradient(circle, #ffd700, #ff6b6b);
  border-radius: 50%;
  animation: magicParticle 2s ease-out forwards;
}

.particle-sparkle {
  width: 8rpx;
  height: 8rpx;
  background: radial-gradient(circle, #fff, #ffd700);
  border-radius: 50%;
  animation: sparkleParticle 1.5s ease-out forwards;
}

.particle-star {
  width: 10rpx;
  height: 10rpx;
  background: radial-gradient(circle, #fff, #00d4ff);
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  animation: starParticle 2s ease-out forwards;
}

.particle-confetti {
  width: 12rpx;
  height: 4rpx;
  background: linear-gradient(45deg, #ff6b6b, #ffd700, #4ecdc4, #45b7d1);
  animation: confettiParticle 3s ease-out forwards;
}

.particle-diamond {
  width: 8rpx;
  height: 8rpx;
  background: linear-gradient(45deg, #fff, #e0e0e0);
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  animation: diamondParticle 2.5s ease-out forwards;
}

.magic-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400rpx;
  height: 400rpx;
  pointer-events: none;
  z-index: 5;
  
  &.active .circle-ring {
    animation-play-state: running;
  }
}

.circle-ring {
  position: absolute;
  border: 2rpx solid rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  animation-play-state: paused;
  
  &.ring-1 {
    width: 100%;
    height: 100%;
    animation: magicRing1 2s ease-out;
  }
  
  &.ring-2 {
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
    border-color: rgba(255, 107, 107, 0.6);
    animation: magicRing2 2s ease-out 0.2s;
  }
  
  &.ring-3 {
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
    border-color: rgba(78, 205, 196, 0.6);
    animation: magicRing3 2s ease-out 0.4s;
  }
}

.light-rays {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  height: 600rpx;
  pointer-events: none;
  z-index: 1;
  
  &.active .light-ray {
    animation-play-state: running;
  }
}

.light-ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4rpx;
  height: 300rpx;
  background: linear-gradient(to top, transparent, rgba(255, 255, 255, 0.8), transparent);
  transform-origin: bottom center;
  animation: lightRay 3s ease-in-out infinite;
  animation-play-state: paused;
}

.energy-burst {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 8;
  
  &.active .burst-ring {
    animation-play-state: running;
  }
}

.burst-ring {
  width: 100rpx;
  height: 100rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: energyBurst 1s ease-out;
  animation-play-state: paused;
}

// Animations
@keyframes preDraw {
  0% { transform: scale(1) rotateY(0deg); }
  100% { transform: scale(0.9) rotateY(0deg); }
}

@keyframes cardFlip {
  0% { transform: scale(0.9) rotateY(0deg); }
  50% { transform: scale(1.2) rotateY(90deg); }
  100% { transform: scale(1.1) rotateY(180deg); }
}

@keyframes cardSettle {
  0% { transform: scale(1.1) rotateY(180deg); }
  100% { transform: scale(1) rotateY(180deg); }
}

@keyframes cardReveal {
  0% { transform: scale(1) rotateY(180deg); }
  50% { transform: scale(1.1) rotateY(180deg); }
  100% { transform: scale(1.05) rotateY(180deg); }
}

@keyframes cardGlow {
  0% { box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3); }
  100% { box-shadow: 0 20rpx 60rpx rgba(255, 215, 0, 0.4); }
}

@keyframes cardCelebrate {
  0% { transform: scale(1) rotateY(180deg); }
  25% { transform: scale(1.1) rotateY(180deg) rotateZ(5deg); }
  50% { transform: scale(1.2) rotateY(180deg) rotateZ(-5deg); }
  75% { transform: scale(1.1) rotateY(180deg) rotateZ(3deg); }
  100% { transform: scale(1.2) rotateY(180deg) rotateZ(0deg); }
}

@keyframes celebrationPeak {
  0% { transform: scale(1.2) rotateY(180deg); }
  100% { transform: scale(1.2) rotateY(180deg); }
}

@keyframes celebrationEnd {
  0% { transform: scale(1.2) rotateY(180deg); }
  100% { transform: scale(1) rotateY(180deg); }
}

@keyframes patternPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

@keyframes magicParticle {
  0% { opacity: 0; transform: scale(0) translateY(0); }
  50% { opacity: 1; transform: scale(1.5) translateY(-50rpx); }
  100% { opacity: 0; transform: scale(0) translateY(-100rpx); }
}

@keyframes sparkleParticle {
  0% { opacity: 0; transform: scale(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(2) rotate(180deg); }
  100% { opacity: 0; transform: scale(0) rotate(360deg); }
}

@keyframes starParticle {
  0% { opacity: 0; transform: scale(0) translateY(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(1.5) translateY(-30rpx) rotate(180deg); }
  100% { opacity: 0; transform: scale(0) translateY(-60rpx) rotate(360deg); }
}

@keyframes confettiParticle {
  0% { opacity: 1; transform: translateY(0) rotate(0deg); }
  100% { opacity: 0; transform: translateY(-200rpx) rotate(720deg); }
}

@keyframes diamondParticle {
  0% { opacity: 0; transform: scale(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(1.2) rotate(45deg); }
  100% { opacity: 0; transform: scale(0) rotate(90deg); }
}

@keyframes magicRing1 {
  0% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 0; transform: scale(1.2); }
}

@keyframes magicRing2 {
  0% { opacity: 0; transform: scale(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(1) rotate(180deg); }
  100% { opacity: 0; transform: scale(1.2) rotate(360deg); }
}

@keyframes magicRing3 {
  0% { opacity: 0; transform: scale(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(1) rotate(-180deg); }
  100% { opacity: 0; transform: scale(1.2) rotate(-360deg); }
}

@keyframes lightRay {
  0% { opacity: 0; transform: rotate(var(--rotation, 0deg)) scaleY(0); }
  50% { opacity: 1; transform: rotate(var(--rotation, 0deg)) scaleY(1); }
  100% { opacity: 0; transform: rotate(var(--rotation, 0deg)) scaleY(0); }
}

@keyframes energyBurst {
  0% { opacity: 1; transform: scale(0); }
  50% { opacity: 0.8; transform: scale(5); }
  100% { opacity: 0; transform: scale(10); }
}
</style>
