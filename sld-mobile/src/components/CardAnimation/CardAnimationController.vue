<template>
  <view class="card-animation-controller">
    <!-- Three.js Animation (H5 only) -->
    <ThreeJSCardAnimation
      v-if="useThreeJS"
      :cardImage="cardImage"
      :isAnimating="isAnimating"
      :animationType="animationType"
      @animationComplete="handleAnimationComplete"
    />
    
    <!-- CSS Animation (Fallback for all platforms) -->
    <CSSCardAnimation
      v-else
      :cardImage="cardImage"
      :isAnimating="isAnimating"
      :animationType="animationType"
      @animationComplete="handleAnimationComplete"
    />
    
    <!-- Sound effects -->
    <view class="sound-effects" v-if="enableSound">
      <!-- Audio elements will be added here -->
    </view>
  </view>
</template>

<script>
import ThreeJSCardAnimation from './ThreeJSCardAnimation.vue'
import CSSCardAnimation from './CSSCardAnimation.vue'

export default {
  name: 'CardAnimationController',
  components: {
    ThreeJSCardAnimation,
    CSSCardAnimation
  },
  props: {
    cardImage: {
      type: String,
      default: ''
    },
    isAnimating: {
      type: Boolean,
      default: false
    },
    animationType: {
      type: String,
      default: 'draw', // 'draw', 'reveal', 'celebrate'
      validator: (value) => ['draw', 'reveal', 'celebrate'].includes(value)
    },
    enableSound: {
      type: Boolean,
      default: true
    },
    forceCSS: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      useThreeJS: false,
      isH5: false,
      webGLSupported: false
    }
  },
  mounted() {
    this.detectEnvironment()
  },
  methods: {
    detectEnvironment() {
      // #ifdef H5
      this.isH5 = true
      this.webGLSupported = this.checkWebGLSupport()
      // #endif
      
      // #ifdef MP
      this.isH5 = false
      this.webGLSupported = false
      // #endif
      
      // #ifdef APP-PLUS
      this.isH5 = false
      this.webGLSupported = false
      // #endif
      
      // Determine which animation system to use
      this.useThreeJS = this.isH5 && this.webGLSupported && !this.forceCSS
      
      console.log('Animation Environment:', {
        isH5: this.isH5,
        webGLSupported: this.webGLSupported,
        useThreeJS: this.useThreeJS,
        forceCSS: this.forceCSS
      })
    },
    
    checkWebGLSupport() {
      try {
        const canvas = document.createElement('canvas')
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
        return !!gl
      } catch (e) {
        return false
      }
    },
    
    handleAnimationComplete() {
      this.$emit('animationComplete')
    },
    
    // Public methods for external control
    startDrawAnimation() {
      this.$emit('update:animationType', 'draw')
      this.$emit('update:isAnimating', true)
    },
    
    startRevealAnimation() {
      this.$emit('update:animationType', 'reveal')
      this.$emit('update:isAnimating', true)
    },
    
    startCelebrationAnimation() {
      this.$emit('update:animationType', 'celebrate')
      this.$emit('update:isAnimating', true)
    },
    
    stopAnimation() {
      this.$emit('update:isAnimating', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.card-animation-controller {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.sound-effects {
  position: absolute;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
</style>
