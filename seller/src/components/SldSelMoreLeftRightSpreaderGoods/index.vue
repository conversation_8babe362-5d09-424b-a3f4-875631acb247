<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="props.modalVisible"
      :width="props.width"
      @ok="sldConfirm"
      @cancel="sldCancle"
    >
      <template #footer>
        <Button key="back" @click="sldCancle">{{
          props.cancelBtnText ? props.cancelBtnText : L('取消')
        }}</Button>
        <Button
          key="submit"
          type="primary"
          :loading="props.confirmBtnLoading"
          @click="sldConfirm"
          >{{ props.confirmBtnText ? props.confirmBtnText : L('确认') }}</Button
        >
      </template>
      <div class="component_sele_more flex_column_start_start">
        <div class="tableListForm">
          <div style="position: relative">
            <BasicForm ref="formRef" submitOnReset @register="registerForm" class="basic-form-sld">
            </BasicForm>
          </div>
        </div>
        <div class="content flex_row_start_start" :style="{ height: height + 'px' }">
          <div
            class="scroll_box"
            :style="{ height: height + 'px', background: '#f5f5f5', width: '438px', zIndex: 1 }"
            @scroll="handleScrollLeft"
          >
            <div class="left flex_row_start_start" :style="{ height: height + 'px' }">
              <template v-if="data.list != undefined && data.list.length > 0">
                <a
                  v-for="(item, index) in data.list"
                  :key="index"
                  class="item flex_row_start_start"
                  :style="{ marginBottom: index == data.list.length - 1 ? 10 : 0 }"
                  @click="handleLeftItem(item)"
                >
                  <!-- <div class="mask flex_row_center_center" v-if="item.activityState != 1">已参加其他活动</div>
                  <div class="mask flex_row_center_center" v-if="item.activityState == 1 && item.goodsStock == 0">暂时无货</div> -->
                  <div class="item_left flex_row_center_center">
                    <img :src="item.mainImage" alt="" class="live_img" />
                    <span class="storage flex_row_center_center" v-if="item.activityState == 1">
                      {{ L('库存') }}{{ item.goodsStock }}
                    </span>
                  </div>
                  <div class="item_right flex_column_start_start">
                    <span class="svideo_name">{{ item.goodsName }}</span>
                    <span class="svideo_label">¥{{ item.goodsPrice }}</span>
                    <div class="sele_svideo_flag" v-if="selectedRowKeys.indexOf(item.goodsId) > -1">
                      <AliSvgIcon
                        iconName="iconxuanzhongshangpin"
                        width="19px"
                        height="19px"
                        fillColor="#FF711E"
                      />
                    </div>
                  </div>
                </a>
              </template>
              <template v-if="data.list == undefined || data.list.length == 0">
                <div class="flex_column_center_center" :style="{ width: '100%', height: '100%' }">
                  <Empty :image="simpleImage"></Empty>
                </div>
              </template>
            </div>
          </div>
          <div class="center flex_row_center_center">
            <AliSvgIcon iconName="iconmove-up1" width="39px" height="32px" fillColor="#fff6f4" />
          </div>
          <div
            class="scroll_box"
            :style="{ height: height + 'px', background: '#f5f5f5', width: '438px', zIndex: 1 }"
          >
            <div class="right flex_row_start_start" :style="{ height: height + 'px' }">
              <template v-if="selectedRows.length > 0">
                <div
                  v-for="(item, index) in selectedRows"
                  :key="index"
                  class="right_item_wrap flex_column_start_start"
                  :style="{ marginBottom: index == selectedRows.length - 1 ? 10 : 0 }"
                >
                  <div class="item flex_row_start_start" @click="handleRightItem(item)">
                    <div class="item_left flex_row_center_center">
                      <img :src="item.mainImage" alt="" class="live_img" />
                    </div>
                    <div class="item_right flex_column_start_start">
                      <span class="svideo_name">{{ item.goodsName }}</span>
                      <span class="svideo_label">¥{{ item.goodsPrice }}</span>
                      <div class="sele_svideo_flag">
                        <AliSvgIcon
                          iconName="iconshanchushangpin"
                          width="19px"
                          height="19px"
                          fillColor="#FF711E"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="spreader_goods_info">
                    <!-- 佣金类型选择行 -->
                    <div class="info_row">
                      <div class="info_item">
                        <span class="must">*</span>
                        <span class="label">{{ L('佣金类型') }}:</span>
                        <Select
                          :placeholder="L('请选择')"
                          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                          v-model:value="item.commissionType"
                          class="select_input"
                          @change="handleCommissionTypeChange($event, item.goodsId)"
                        >
                          <Select.Option value="fixed">{{ L('固定佣金') }}</Select.Option>
                          <Select.Option value="rate">{{ L('佣金比例') }}</Select.Option>
                        </Select>
                      </div>
                    </div>

                    <!-- 佣金数值输入行 -->
                    <div class="info_row">
                      <div class="info_item">
                        <span class="must">*</span>
                        <span class="label">
                          {{ item.commissionType === 'fixed' ? L('佣金金额') : L('佣金比例') }}:
                        </span>
                        <template v-if="item.commissionType === 'fixed'">
                          <InputNumber
                            :min="0.01"
                            :max="Number(item.goodsPrice) * 1 - 0.01"
                            :precision="2"
                            class="number_input"
                            v-model:value="item.commission"
                            :placeholder="L('请输入金额')"
                            @change="handleCommission($event, item.goodsId)"
                            @blur="
                              blurCommission(
                                $event,
                                item.goodsId,
                                Number(item.goodsPrice) * 1 - 0.01,
                              )
                            "
                          >
                            <template #addonAfter>元</template>
                          </InputNumber>
                        </template>
                        <template v-else>
                          <InputNumber
                            :min="1"
                            :max="99"
                            :precision="0"
                            :step="1"
                            :parser="parseIntegerOnly"
                            :formatter="formatIntegerOnly"
                            class="number_input"
                            v-model:value="item.commissionRateDisplay"
                            :placeholder="L('请输入比例')"
                            @change="handleCommissionRate($event, item.goodsId)"
                            @blur="blurCommissionRate($event, item.goodsId)"
                            @keypress="preventDecimalInput"
                          >
                            <template #addonAfter>%</template>
                          </InputNumber>
                        </template>
                      </div>
                    </div>

                    <!-- 提示信息行 -->
                    <div class="info_row">
                      <div class="tip_text">
                        <template v-if="item.commissionType === 'fixed'">
                          {{ L('最大可设置') }}: ¥{{
                            (Number(item.goodsPrice) * 1 - 0.01).toFixed(2)
                          }}
                        </template>
                        <template v-else>
                          {{ L('预计佣金') }}: ¥{{
                            ((Number(item.goodsPrice) * (item.commissionRateDisplay || 0)) / 100).toFixed(
                              2,
                            )
                          }}
                        </template>
                      </div>
                    </div>

                    <!-- 标签选择行 -->
                    <div class="info_row">
                      <div class="info_item">
                        <span class="must">*</span>
                        <span class="label">{{ L('商品标签') }}:</span>
                        <Select
                          :placeholder="L('请选择标签')"
                          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                          v-model:value="item.labelId"
                          class="select_input"
                        >
                          <Select.Option
                            v-for="(labelItem, index) in label_data"
                            :key="index"
                            :value="labelItem.labelId"
                          >
                            {{ labelItem.labelName }}
                          </Select.Option>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="flex_column_center_center" :style="{ width: '100%', height: '100%' }">
                  <Empty :image="simpleImage" :description="L('您还未选择数据')"></Empty>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'SldSelMoreLeftRightSpreaderGoods',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted, computed, watch } from 'vue';
  import { Modal, Button, Empty, InputNumber, Select } from 'ant-design-vue';
  import { failTip } from '@/utils/utils';
  import { getSpreaderGoodsListApi } from '/@/api/common/common';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { list_com_page_size_16 } from '/@/utils/utils';
  import { validatorEmoji } from '/@/utils/validate';
  import { getSpreaderGoodsLabelListApi } from '/@/api/spreader/goods_list';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const label_data = ref([]); //推手商品标签列表
  const selectedRows = ref([]);
  const selectedRowKeys = ref([]); //selectedRows的key
  const loading = ref(false);
  const data = ref({});
  const formValues = ref({});
  const loading_pagination_flag = ref({});
  const title = ref('');
  const params = ref({ pageSize: list_com_page_size_16 }); //搜索条件
  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

  const props = defineProps({
    title: { type: String, required: true }, //弹框标题
    modalVisible: { type: Boolean, required: true }, //弹框是否显示
    confirmBtnLoading: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    height: { type: Number }, //弹框内容高度
    width: { type: Number, default: 416 }, //弹框宽度，默认416px
    confirmBtnText: { type: String }, //弹框底部确认按钮显示文案，默认为确认
    cancelBtnText: { type: String }, //弹框底部取消按钮显示文案，默认为取消
    selectedRow: { type: Array, required: true, default: () => [] }, //表单数据
    selectedRowKey: { type: Array, required: true, default: () => [] }, //表单数据
    extra: { type: Object }, //表单数据
  });

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });

  watch(modalVisible, () => {
    if (modalVisible.value) {
      get_list({ pageSize: list_com_page_size_16 });
      selectedRows.value = [...props.selectedRow];
      selectedRowKeys.value = [...props.selectedRowKey];
    }
  });

  // 获取数据
  const get_list = async (param) => {
    let new_params = { ...param, ...formValues.value };
    let res = await getSpreaderGoodsListApi(new_params);
    if (res.state == 200) {
      if (res.data.pagination != undefined) {
        if (res.data.pagination.current == 1) {
          data.value = res.data;
        } else {
          data.value.list = data.value.list.concat(res.data.list);
          data.value.pagination = res.data.pagination;
        }
      }
      loading_pagination_flag.value = false;
    }
  };

  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'goodsName',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入商品名称'),
          size: 'default',
        },
        label: L('商品名称'),
        labelWidth: 70,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
    ],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  // 搜索
  async function search() {
    await validate();
    let values = getFieldsValue();
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    params.value = { pageSize: list_com_page_size_16 };
    formValues.value = values;
    get_list({ ...params.value });
  }

  async function reset() {
    params.value.current = 1;
  }

  // 确定
  const sldConfirm = () => {
    if (selectedRowKeys.value.length > 0) {
      if (
        props.extra.min_num != undefined &&
        props.extra.min_num > 0 &&
        selectedRowKeys.value.length < props.extra.min_num
      ) {
        failTip(`${L('该模块至少需要选择')}${props.extra.min_num}${L('个商品')}`);
        return false;
      }
      if (
        props.extra.total_num != undefined &&
        props.extra.total_num > 0 &&
        selectedRowKeys.value.length != props.extra.total_num
      ) {
        failTip(`${L('该模块需要选择')}${props.extra.total_num}${L('个商品')}`); //该模块需要选择   个商品
        return false;
      }
      if (
        props.extra.max_num != undefined &&
        props.extra.max_num > 0 &&
        selectedRowKeys.value.length > props.extra.max_num
      ) {
        failTip(`${L('该模块最多选择')}${props.extra.max_num}${L('个商品')}`);
        return false;
      }

      // 验证每个商品的佣金设置
      for (let item of selectedRows.value) {
        if (item.commissionType === 'fixed') {
          if (!item.commission || item.commission <= 0) {
            failTip(`${L('商品')} "${item.goodsName}" ${L('的固定佣金不能为空或小于等于0')}`);
            return false;
          }
          if (item.commission >= Number(item.goodsPrice)) {
            failTip(`${L('商品')} "${item.goodsName}" ${L('的固定佣金不能大于等于商品价格')}`);
            return false;
          }
        } else if (item.commissionType === 'rate') {
          if (!item.commissionRateDisplay || item.commissionRateDisplay <= 0) {
            failTip(`${L('商品')} "${item.goodsName}" ${L('的佣金比例不能为空或小于等于0')}`);
            return false;
          }
          if (item.commissionRateDisplay > 99) {
            failTip(`${L('商品')} "${item.goodsName}" ${L('的佣金比例不能大于99%')}`);
            return false;
          }
        }

        if (!item.labelId) {
          failTip(`${L('商品')} "${item.goodsName}" ${L('必须选择标签')}`);
          return false;
        }
      }

      emit('confirmEvent', selectedRows.value, selectedRowKeys.value);
    } else {
      failTip(L('请选择商品')); //请选择商品
    }
  };

  // 取消
  const sldCancle = () => {
    selectedRows.value = [];
    selectedRowKeys.value = [];
    params.value = { pageSize: list_com_page_size_16 };
    formValues.value = {};
    emit('cancleEvent');
  };

  const handleScrollLeft = (e) => {
    //e.srcElement.scrollTop: 滚动条距离页面顶部距离
    //e.srcElement.clientHeight: 滚动页面高度
    //e.srcElement.scrollHeight: 滚动条高度
    // if (e.srcElement.scrollTop + e.srcElement.clientHeight > e.srcElement.scrollHeight - 10) {//-50距离底部50px是触发以下内容
    // }
    if (
      data.value.pagination.current * list_com_page_size_16 < data.value.pagination.total &&
      !loading_pagination_flag.value
    ) {
      //请求分页数据
      loading_pagination_flag.value = true;
      get_list({ pageSize: list_com_page_size_16, current: data.value.pagination.current * 1 + 1 });
    }
  };

  //左侧数据点击事件（将选中的数据添加到右侧，左侧添加选中标识）
  const handleLeftItem = (item) => {
    if (selectedRowKeys.value.indexOf(item.goodsId) == -1) {
      selectedRowKeys.value.push(item.goodsId);

      // 设置默认佣金类型为固定佣金
      if (item.commissionType == undefined) {
        item.commissionType = 'fixed';
      }

      // 根据佣金类型设置默认值
      if (item.commissionType === 'fixed') {
        if (item.commission != undefined && item.commission) {
          item.commission = item.commission;
        } else {
          item.commission = 0.01;
        }
        item.commissionRate = undefined;
        item.commissionRateDisplay = undefined;
      } else {
        if (item.commissionRateDisplay != undefined && item.commissionRateDisplay) {
          item.commissionRateDisplay = item.commissionRateDisplay;
          item.commissionRate = item.commissionRateDisplay / 100; // 转换为小数
        } else {
          item.commissionRateDisplay = 1; // 显示值为1%
          item.commissionRate = 0.01; // 实际值为0.01
        }
        item.commission = undefined;
      }

      if (item.labelId != undefined && item.labelId) {
        item.labelId = item.labelId;
      } else {
        item.labelId = label_data.value.length > 0 ? label_data.value[0].labelId : '';
      }
      selectedRows.value.push(item);
    }
  };

  //右侧数据点击事件（移除选中数据，右侧将不显示，左侧的选中标识去掉）
  const handleRightItem = (item) => {
    selectedRows.value = selectedRows.value.filter((items) => items.goodsId != item.goodsId);
    selectedRowKeys.value = selectedRowKeys.value.filter((items) => items != item.goodsId);
  };

  //获取所有标签
  const get_label_list = async () => {
    try {
      let res = await getSpreaderGoodsLabelListApi({});
      if (res.state == 200) {
        if (res.data.length == 0) {
          failTip(L('平台后台未添加商品标签，无法导入商品'));
        }
        label_data.value = res.data;
      }
    } catch (error) {}
  };

  // 处理佣金类型变化
  const handleCommissionTypeChange = (type, goodsId) => {
    let target = selectedRows.value.filter((items) => items.goodsId == goodsId)[0];
    target.commissionType = type;
    // 切换类型时重置数值
    if (type === 'fixed') {
      target.commission = 0.01;
      target.commissionRate = undefined;
      target.commissionRateDisplay = undefined;
    } else {
      target.commissionRateDisplay = 1; // 显示值为1%
      target.commissionRate = 0.01; // 实际值为0.01
      target.commission = undefined;
    }
  };

  const handleCommission = (e, goodsId) => {
    let target = selectedRows.value.filter((items) => items.goodsId == goodsId)[0];
    target.commission = e;
  };

  const handleCommissionRate = (e, goodsId) => {
    let target = selectedRows.value.filter((items) => items.goodsId == goodsId)[0];
    // 确保输入值为整数，范围1-99
    let displayValue = parseInt(e) || 1;
    if (displayValue > 99) {
      displayValue = 99;
    } else if (displayValue < 1) {
      displayValue = 1;
    }
    target.commissionRateDisplay = displayValue; // 保存显示值（如2）
    target.commissionRate = displayValue / 100; // 保存实际值（如0.02）
  };

  // 右边框中的固定佣金输入框失去焦点事件
  const blurCommission = (e, goodsId, num) => {
    e = e.target.value;
    let target = selectedRows.value.filter((items) => items.goodsId == goodsId)[0];
    if (num < e) {
      target.commission = num;
    } else {
      target.commission = e.length != 0 ? e : 0.01;
    }
  };

  // 右边框中的佣金比例输入框失去焦点事件
  const blurCommissionRate = (e, goodsId) => {
    e = e.target.value;
    let target = selectedRows.value.filter((items) => items.goodsId == goodsId)[0];

    // 确保输入值为整数，范围1-99
    let displayValue = parseInt(e) || 1;
    if (displayValue > 99) {
      displayValue = 99;
    } else if (displayValue < 1) {
      displayValue = 1;
    }

    target.commissionRateDisplay = displayValue; // 显示值（如2）
    target.commissionRate = displayValue / 100; // 实际值（如0.02）
  };

  // 解析器：只允许整数输入
  const parseIntegerOnly = (value) => {
    if (!value) return '';
    // 移除所有非数字字符，包括小数点
    const numericValue = value.toString().replace(/[^\d]/g, '');
    const intValue = parseInt(numericValue) || '';
    return intValue.toString();
  };

  // 格式化器：确保显示为整数
  const formatIntegerOnly = (value) => {
    if (!value) return '';
    const intValue = parseInt(value) || '';
    return intValue.toString();
  };

  // 阻止小数点输入
  const preventDecimalInput = (event) => {
    // 阻止输入小数点、负号等非数字字符
    const char = String.fromCharCode(event.which);
    if (!/[0-9]/.test(char)) {
      event.preventDefault();
    }
  };

  onMounted(() => {
    get_label_list();
  });
</script>
<style lang="less">
  @import './index.less';
  .basic-form-sld {
    .ant-form-item-with-help {
      margin-bottom: 20px;
    }
  }
</style>
