{"name": "slodon", "version": "2025.08.031", "license": "MIT", "scripts": {"bootstrap": "pnpm install", "build": "NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode production", "build:analyze": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode analyze", "build:docker": "vite build --mode docker", "build:no-cache": "pnpm clean:cache && npm run build", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode test", "commit": "czg", "dev": "pnpm vite", "dev:pro": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite --mode production", "preinstall": "npx only-allow pnpm", "postinstall": "turbo run stub", "lint": "turbo run lint", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write .", "lint:stylelint": "stylelint \"**/*.{vue,css,less,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "prepare": "", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "serve": "npm run dev", "serve:prod": "pnpm vite --mode production", "serve:test": "pnpm vite --mode test", "test:gzip": "npx http-server dist --cors --gzip -c-1", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["prettier --write", "stylelint --fix --allow-empty-input"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@iconify/iconify": "^3.1.1", "@logicflow/core": "^1.2.9", "@logicflow/extension": "^1.2.9", "@vben/hooks": "workspace:*", "@vue/shared": "^3.3.4", "@vueup/vue-quill": "1.0.0-alpha.40", "@vueuse/core": "^10.2.1", "@vueuse/shared": "^10.2.1", "@zxcvbn-ts/core": "^3.0.2", "ant-design-vue": "^3.2.20", "axios": "^1.4.0", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "echarts": "^5.4.2", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "2.1.4", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "swiper": "^11.0.5", "tinymce": "^5.10.7", "vue": "3.3.4", "vue-draggable-plus": "^0.3.3", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.2.4", "vue-router": "^4.2.3", "vue-types": "^5.1.0", "vue3-carousel-3d": "^1.0.4"}, "devDependencies": {"@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@iconify/json": "^2.2.87", "@purge-icons/generated": "^0.9.0", "@types/codemirror": "^5.60.8", "@types/crypto-js": "^4.1.1", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/mockjs": "^1.0.7", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.1", "@types/qs": "^6.9.7", "@types/showdown": "^2.0.1", "@types/sortablejs": "^1.15.1", "@vben/eslint-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/ts-config": "workspace:*", "@vben/types": "workspace:*", "@vben/vite-config": "workspace:*", "@vue/compiler-sfc": "^3.3.4", "@vue/test-utils": "^2.4.0", "cross-env": "^7.0.3", "cz-git": "^1.6.1", "czg": "^1.6.1", "husky": "^8.0.3", "lint-staged": "13.2.3", "prettier": "^2.8.8", "prettier-plugin-packagejson": "^2.4.4", "rimraf": "^5.0.1", "turbo": "1.10.6", "typescript": "^5.1.6", "unbuild": "^1.2.1", "vite": "^4.4.0", "vite-plugin-mock": "^2.9.6", "vue-tsc": "^1.8.4"}, "packageManager": "pnpm@8.1.0", "engines": {"node": ">=16.15.1", "pnpm": ">=8.1.0"}}